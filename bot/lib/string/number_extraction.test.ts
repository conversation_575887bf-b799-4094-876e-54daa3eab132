import { StringHelper } from './index'

describe('数字提取和转换测试', () => {
  describe('extractNumbers - 基本数字提取', () => {
    it('应该提取单个数字', () => {
      expect(StringHelper.extractNumbers('1')).toEqual([1])
      expect(StringHelper.extractNumbers('5')).toEqual([5])
      expect(StringHelper.extractNumbers('13')).toEqual([13])
    })

    it('应该过滤超出范围的数字', () => {
      expect(StringHelper.extractNumbers('0')).toEqual([])
      expect(StringHelper.extractNumbers('14')).toEqual([])
      expect(StringHelper.extractNumbers('100')).toEqual([])
    })

    it('应该提取多个单独的数字', () => {
      expect(StringHelper.extractNumbers('1 5 8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('我选择 3 和 7 还有 12')).toEqual([3, 7, 12])
    })
  })

  describe('extractNumbers - 分隔符格式', () => {
    it('应该处理连字符分隔', () => {
      expect(StringHelper.extractNumbers('1-5-8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('2-6-10')).toEqual([2, 6, 10])
    })

    it('应该处理逗号分隔', () => {
      expect(StringHelper.extractNumbers('1，5，8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('1,5,8')).toEqual([1, 5, 8])
    })

    it('应该处理斜杠分隔', () => {
      expect(StringHelper.extractNumbers('1/5/8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('3/7/11')).toEqual([3, 7, 11])
    })

    it('应该处理空格分隔', () => {
      expect(StringHelper.extractNumbers('1 5 8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('4  6   9')).toEqual([4, 6, 9])
    })

    it('应该处理混合分隔符', () => {
      expect(StringHelper.extractNumbers('1-5，8')).toEqual([1, 5, 8])
      expect(StringHelper.extractNumbers('2/6 10')).toEqual([2, 6, 10])
    })

    it('应该处理两个数字的情况', () => {
      expect(StringHelper.extractNumbers('1-5')).toEqual([1, 5])
      expect(StringHelper.extractNumbers('7，12')).toEqual([7, 12])
      expect(StringHelper.extractNumbers('3/9')).toEqual([3, 9])
    })
  })

  describe('extractNumbers - 极端情况', () => {
    it('应该拆分连续数字 1018', () => {
      const result = StringHelper.extractNumbers('1018')
      expect(result).toContain(10)
      expect(result).toContain(1)
      expect(result).toContain(8)
    })

    it('应该拆分连续数字 1213', () => {
      const result = StringHelper.extractNumbers('1213')
      expect(result).toContain(12)
      expect(result).toContain(13)
    })

    it('应该拆分连续数字 567', () => {
      const result = StringHelper.extractNumbers('567')
      expect(result).toContain(5)
      expect(result).toContain(6)
      expect(result).toContain(7)
    })

    it('应该处理包含无效数字的连续数字', () => {
      const result = StringHelper.extractNumbers('1415')
      expect(result).toContain(1)
      // 14和15都超出范围，应该被过滤
      expect(result).not.toContain(14)
      expect(result).not.toContain(15)
    })

    it('拆分从小到大的数字', async () => {
      const result = StringHelper.extractNumbers('1513')
      expect(result).toContain(1)
      // 14和15都超出范围，应该被过滤
      expect(result).not.toContain(5)
      expect(result).not.toContain(13)
    }, 30000)
  })

  describe('numbersToText - 数字到文字转换', () => {
    it('应该转换生活角色数字 (1-4)', () => {
      expect(StringHelper.numbersToText([1])).toEqual(['职场奋斗者'])
      expect(StringHelper.numbersToText([2])).toEqual(['家庭管理者'])
      expect(StringHelper.numbersToText([3])).toEqual(['退休精进者'])
      expect(StringHelper.numbersToText([4])).toEqual(['修行者'])
    })

    it('应该转换冥想经验数字 (5-7)', () => {
      expect(StringHelper.numbersToText([5])).toEqual(['纯小白'])
      expect(StringHelper.numbersToText([6])).toEqual(['接触过'])
      expect(StringHelper.numbersToText([7])).toEqual(['有基础'])
    })

    it('应该转换人生议题数字 (8-13)', () => {
      expect(StringHelper.numbersToText([8])).toEqual(['情绪减压'])
      expect(StringHelper.numbersToText([9])).toEqual(['专注提升'])
      expect(StringHelper.numbersToText([10])).toEqual(['睡眠改善'])
      expect(StringHelper.numbersToText([11])).toEqual(['财富能量'])
      expect(StringHelper.numbersToText([12])).toEqual(['亲密关系'])
      expect(StringHelper.numbersToText([13])).toEqual(['灵性成长'])
    })

    it('应该转换多个数字', () => {
      expect(StringHelper.numbersToText([1, 5, 8])).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压'
      ])
    })

    it('应该过滤无效数字', () => {
      expect(StringHelper.numbersToText([0, 1, 14, 5])).toEqual([
        '职场奋斗者',
        '纯小白'
      ])
    })
  })

  describe('extractNumbersToText - 完整流程', () => {
    it('应该处理标准分隔符格式', () => {
      expect(StringHelper.extractNumbersToText('1-5-8')).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压'
      ])

      expect(StringHelper.extractNumbersToText('2，6，10')).toEqual([
        '家庭管理者',
        '接触过',
        '睡眠改善'
      ])

      expect(StringHelper.extractNumbersToText('3/7/11')).toEqual([
        '退休精进者',
        '有基础',
        '财富能量'
      ])

      expect(StringHelper.extractNumbersToText('4 5 12')).toEqual([
        '修行者',
        '纯小白',
        '亲密关系'
      ])
    })

    it('应该处理极端情况', () => {
      const result = StringHelper.extractNumbersToText('1018')
      expect(result).toContain('职场奋斗者') // 1
      expect(result).toContain('情绪减压') // 8
      expect(result).toContain('睡眠改善') // 10
    })

    it('应该处理自然语言输入', () => {
      expect(StringHelper.extractNumbersToText('我选择1和5还有8')).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压'
      ])

      expect(StringHelper.extractNumbersToText('答案是：2、7、13')).toEqual([
        '家庭管理者',
        '有基础',
        '灵性成长'
      ])
    })

    it('应该处理空输入和无效输入', () => {
      expect(StringHelper.extractNumbersToText('')).toEqual([])
      expect(StringHelper.extractNumbersToText('没有数字')).toEqual([])
      expect(StringHelper.extractNumbersToText('14-15-16')).toEqual([])
    })

    it('应该去重复数字', () => {
      expect(StringHelper.extractNumbersToText('1-1-5-5-8')).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压'
      ])
    })

    it('应该处理复杂的真实场景', () => {
      // 模拟可能的真实输入
      expect(StringHelper.extractNumbersToText('我的选择是1-5-8，谢谢')).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压'
      ])

      expect(StringHelper.extractNumbersToText('选择：2，6，10。')).toEqual([
        '家庭管理者',
        '接触过',
        '睡眠改善'
      ])

      expect(StringHelper.extractNumbersToText('我觉得是 3/7/11 这几个')).toEqual([
        '退休精进者',
        '有基础',
        '财富能量'
      ])

      expect(StringHelper.extractNumbersToText('1、5、8、10')).toEqual([
        '职场奋斗者',
        '纯小白',
        '情绪减压',
        '睡眠改善'
      ])
    })
  })
})
